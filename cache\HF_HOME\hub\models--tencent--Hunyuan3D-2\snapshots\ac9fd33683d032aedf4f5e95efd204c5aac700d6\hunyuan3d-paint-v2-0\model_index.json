{"_class_name": "StableDiffusionPipeline", "_diffusers_version": "0.23.1", "feature_extractor": ["transformers", "CLIPImageProcessor"], "requires_safety_checker": false, "safety_checker": [null, null], "scheduler": ["diffusers", "DDIMScheduler"], "text_encoder": ["transformers", "CLIPTextModel"], "tokenizer": ["transformers", "CLIPTokenizer"], "unet": ["modules", "UNet2p5DConditionModel"], "vae": ["diffusers", "AutoencoderKL"]}