{"name": "app", "lockfileVersion": 3, "requires": true, "packages": {"": {"dependencies": {"gradio": "^0.1.0"}}, "node_modules/asn1": {"version": "0.1.11", "resolved": "https://registry.npmjs.org/asn1/-/asn1-0.1.11.tgz", "integrity": "sha512-Fh9zh3G2mZ8qM/kwsiKwL2U2FmXxVsboP4x1mXjnhKHv3SmzaBZoYvxEQJz/YS2gnCgd8xlAVWcZnQyC9qZBsA==", "optional": true, "engines": {"node": ">=0.4.9"}}, "node_modules/assert-plus": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/assert-plus/-/assert-plus-0.1.5.tgz", "integrity": "sha512-brU24g7ryhRwGCI2y+1dGQmQXiZF7TtIj583S96y0jjdajIe6wn8BuXyELYhvD22dtIxDQVFk04YTJwwdwOYJw==", "optional": true, "engines": {"node": ">=0.8"}}, "node_modules/async": {"version": "0.9.2", "resolved": "https://registry.npmjs.org/async/-/async-0.9.2.tgz", "integrity": "sha512-l6ToIJIotphWahxxHyzK9bnLR6kM4jJIIgLShZeqLY7iboHoGkdgFl7W2/Ivi4SkMJYGKqW8vSuk0uKUj6qsSw==", "license": "MIT", "optional": true}, "node_modules/aws-sign2": {"version": "0.5.0", "resolved": "https://registry.npmjs.org/aws-sign2/-/aws-sign2-0.5.0.tgz", "integrity": "sha512-oqUX0DM5j7aPWPCnpWebiyNIj2wiNI87ZxnOMoGv0aE4TGlBy2N+5iWc6dQ/NOKZaBD2W6PVz8jtOGkWzSC5EA==", "optional": true, "engines": {"node": "*"}}, "node_modules/bluebird": {"version": "1.0.8", "resolved": "https://registry.npmjs.org/bluebird/-/bluebird-1.0.8.tgz", "integrity": "sha512-e8rlJcByuxPdMiiwU3lGtENflMtUncAblzSlN7rBAZ9ygb75D/rng3Xt5FbZpYqVCzK+sFHVIMht4G6fbfUfbA==", "license": "MIT"}, "node_modules/boom": {"version": "0.4.2", "resolved": "https://registry.npmjs.org/boom/-/boom-0.4.2.tgz", "integrity": "sha512-OvfN8y1oAxxphzkl2SnCS+ztV/uVKTATtgLjWYg/7KwcNyf3rzpHxNQJZCKtsZd4+MteKczhWbSjtEX4bGgU9g==", "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial).", "optional": true, "dependencies": {"hoek": "0.9.x"}, "engines": {"node": ">=0.8.0"}}, "node_modules/clarinet": {"version": "0.8.1", "resolved": "https://registry.npmjs.org/clarinet/-/clarinet-0.8.1.tgz", "integrity": "sha512-0jIPAg6gXcr3GzcLbRT2CuNGaADlOhso35o//c5ye463twKEaWrJGTrvqCTbePxolEBEIZ6rDsldUVQL1/1wrw==", "engines": {"chrome": ">=16.0.912", "firefox": ">=0.8.0", "node": ">=0.3.6"}}, "node_modules/coffee-script": {"version": "1.7.1", "resolved": "https://registry.npmjs.org/coffee-script/-/coffee-script-1.7.1.tgz", "integrity": "sha512-W3s+SROY73OmrSGtPTTW/2wp2rmW5vuh0/tUuCK1NvTuyzLOVPccIP9whmhZ4cYWcr2NJPNENZIFaAMkTD5G3w==", "deprecated": "CoffeeScript on NPM has moved to \"coffeescript\" (no hyphen)", "license": "MIT", "dependencies": {"mkdirp": "~0.3.5"}, "bin": {"cake": "bin/cake", "coffee": "bin/coffee"}, "engines": {"node": ">=0.8.0"}}, "node_modules/combined-stream": {"version": "0.0.7", "resolved": "https://registry.npmjs.org/combined-stream/-/combined-stream-0.0.7.tgz", "integrity": "sha512-qfexlmLp9MyrkajQVyjEDb0Vj+KhRgR/rxLiVhaihlT+ZkX0lReqtH6Ack40CvMDERR4b5eFp3CreskpBs1Pig==", "optional": true, "dependencies": {"delayed-stream": "0.0.5"}, "engines": {"node": ">= 0.8"}}, "node_modules/cryptiles": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/cryptiles/-/cryptiles-0.2.2.tgz", "integrity": "sha512-gvWSbgqP+569DdslUiCelxIv3IYK5Lgmq1UrRnk+s1WxQOQ16j3GPDcjdtgL5Au65DU/xQi6q3xPtf5Kta+3IQ==", "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial).", "optional": true, "dependencies": {"boom": "0.4.x"}, "engines": {"node": ">=0.8.0"}}, "node_modules/ctype": {"version": "0.5.3", "resolved": "https://registry.npmjs.org/ctype/-/ctype-0.5.3.tgz", "integrity": "sha512-T6CEkoSV4q50zW3TlTHMbzy1E5+zlnNcY+yb7tWVYlTwPhx9LpnfAkd4wecpWknDyptp4k97LUZeInlf6jdzBg==", "optional": true, "engines": {"node": ">= 0.4"}}, "node_modules/delayed-stream": {"version": "0.0.5", "resolved": "https://registry.npmjs.org/delayed-stream/-/delayed-stream-0.0.5.tgz", "integrity": "sha512-v+7uBd1pqe5YtgPacIIbZ8HuHeLFVNe4mUEyFDXL6KiqzEykjbw+5mXZXpGFgNVasdL4jWKgaKIXrEHiynN1LA==", "optional": true, "engines": {"node": ">=0.4.0"}}, "node_modules/faye-websocket": {"version": "0.11.4", "resolved": "https://registry.npmjs.org/faye-websocket/-/faye-websocket-0.11.4.tgz", "integrity": "sha512-CzbClwlXAuiRQAlUyfqPgvPoNKTckTPGfwZV4ZdAhVcP2lh9KUxJg2b5GkE7XbjKQ3YJnQ9z6D9ntLAlB+tP8g==", "license": "Apache-2.0", "dependencies": {"websocket-driver": ">=0.5.1"}, "engines": {"node": ">=0.8.0"}}, "node_modules/forever-agent": {"version": "0.5.2", "resolved": "https://registry.npmjs.org/forever-agent/-/forever-agent-0.5.2.tgz", "integrity": "sha512-PDG5Ef0Dob/JsZUxUltJOhm/Y9mlteAE+46y3M9RBz/Rd3QVENJ75aGRhN56yekTUboaBIkd8KVWX2NjF6+91A==", "engines": {"node": "*"}}, "node_modules/form-data": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/form-data/-/form-data-0.1.4.tgz", "integrity": "sha512-x8eE+nzFtAMA0YYlSxf/Qhq6vP1f8wSoZ7Aw1GuctBcmudCNuTUmmx45TfEplyb6cjsZO/jvh6+1VpZn24ez+w==", "optional": true, "dependencies": {"async": "~0.9.0", "combined-stream": "~0.0.4", "mime": "~1.2.11"}, "engines": {"node": ">= 0.8"}}, "node_modules/gradio": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/gradio/-/gradio-0.1.0.tgz", "integrity": "sha512-EpUQLXA2Jnzwm8gm55UIZ7SZ10rYe5fpZ6gWPfeH/bi4dtr2VGF4JWyhss18DiCfi8YPop+dvDs/qPkqWe4OMw==", "license": "MIT", "dependencies": {"bluebird": "1.0.x", "coffee-script": "1.7.x", "jandal": "0.0.x", "log_": "0.0.x", "oboe": "1.12.x", "request": "2.34.x", "sockjs": "0.3.x", "uuid": "1.4.x"}}, "node_modules/hawk": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/hawk/-/hawk-1.0.0.tgz", "integrity": "sha512-Sg+VzrI7TjUomO0rjD6UXawsj50ykn5sB/xKNW/IenxzRVyw/wt9A2FLzYpGL/r0QG5hyXY8nLx/2m8UutoDcg==", "deprecated": "This module moved to @hapi/hawk. Please make sure to switch over as this distribution is no longer supported and may contain bugs and critical security issues.", "optional": true, "dependencies": {"boom": "0.4.x", "cryptiles": "0.2.x", "hoek": "0.9.x", "sntp": "0.2.x"}, "engines": {"node": ">=0.8.0"}}, "node_modules/hoek": {"version": "0.9.1", "resolved": "https://registry.npmjs.org/hoek/-/hoek-0.9.1.tgz", "integrity": "sha512-ZZ6eGyzGjyMTmpSPYVECXy9uNfqBR7x5CavhUaLOeD6W0vWK1mp/b7O3f86XE0Mtfo9rZ6Bh3fnuw9Xr8MF9zA==", "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial).", "optional": true, "engines": {"node": ">=0.8.0"}}, "node_modules/http-parser-js": {"version": "0.5.10", "resolved": "https://registry.npmjs.org/http-parser-js/-/http-parser-js-0.5.10.tgz", "integrity": "sha512-Pysuw9XpUq5dVc/2SMHpuTY01RFl8fttgcyunjL7eEMhGM3cI4eOmiCycJDVCo/7O7ClfQD3SaI6ftDzqOXYMA==", "license": "MIT"}, "node_modules/http-signature": {"version": "0.10.1", "resolved": "https://registry.npmjs.org/http-signature/-/http-signature-0.10.1.tgz", "integrity": "sha512-coK8uR5rq2IMj+Hen+sKPA5ldgbCc1/spPdKCL1Fw6h+D0s/2LzMcRK0Cqufs1h0ryx/niwBHGFu8HC3hwU+lA==", "license": "MIT", "optional": true, "dependencies": {"asn1": "0.1.11", "assert-plus": "^0.1.5", "ctype": "0.5.3"}, "engines": {"node": ">=0.8"}}, "node_modules/jandal": {"version": "0.0.15", "resolved": "https://registry.npmjs.org/jandal/-/jandal-0.0.15.tgz", "integrity": "sha512-HTz/RUaWpYxZsekaG6Gvw2IsTgXtdWZTxYqwHTKVtPSR6mDlnCW6liY/bcnrNs7LYX2YfLZmVA6L7pVJVoMM2A==", "license": "MIT"}, "node_modules/json-stringify-safe": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz", "integrity": "sha512-ZClg6AaYvamvYEE82d3Iyd3vSSIjQ+odgjaTzRuO3s7toCdFKczob2i0zCh7JE8kWn17yvAWhUVxvqGwUalsRA==", "license": "ISC"}, "node_modules/log_": {"version": "0.0.4", "resolved": "https://registry.npmjs.org/log_/-/log_-0.0.4.tgz", "integrity": "sha512-3sxGIYgwJ1f714ETc2i0eHW3j2W3ewCOMbrXvI+eNodRfkZCFiOTFAjbPhLNPS7+qs2wMQPtzCdRcg7B3MvvnA==", "license": "MIT"}, "node_modules/mime": {"version": "1.2.11", "resolved": "https://registry.npmjs.org/mime/-/mime-1.2.11.tgz", "integrity": "sha512-Ysa2F/nqTNGHhhm9MV8ure4+Hc+Y8AWiqUdHxsO7xu8zc92ND9f3kpALHjaP026Ft17UfxrMt95c50PLUeynBw=="}, "node_modules/mkdirp": {"version": "0.3.5", "resolved": "https://registry.npmjs.org/mkdirp/-/mkdirp-0.3.5.tgz", "integrity": "sha512-8OCq0De/h9ZxseqzCH8Kw/Filf5pF/vMI6+BH7Lu0jXz2pqYCjTAQRolSxRIi+Ax+oCCjlxoJMP0YQ4XlrQNHg==", "deprecated": "Legacy versions of mkdirp are no longer supported. Please update to mkdirp 1.x. (Note that the API surface has changed to use Promises in 1.x.)", "license": "MIT"}, "node_modules/node-uuid": {"version": "1.4.8", "resolved": "https://registry.npmjs.org/node-uuid/-/node-uuid-1.4.8.tgz", "integrity": "sha512-TkCET/3rr9mUuRp+CpO7qfgT++aAxfDRaalQhwPFzI9BY/2rCDn6OfpZOVggi1AXfTPpfkTrg5f5WQx5G1uLxA==", "deprecated": "Use uuid module instead", "bin": {"uuid": "bin/uuid"}}, "node_modules/oauth-sign": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/oauth-sign/-/oauth-sign-0.3.0.tgz", "integrity": "sha512-Tr31Sh5FnK9YKm7xTUPyDMsNOvMqkVDND0zvK/Wgj7/H9q8mpye0qG2nVzrnsvLhcsX5DtqXD0la0ks6rkPCGQ==", "optional": true, "engines": {"node": "*"}}, "node_modules/oboe": {"version": "1.12.4", "resolved": "https://registry.npmjs.org/oboe/-/oboe-1.12.4.tgz", "integrity": "sha512-B19SoK5K/NVRvMKmLhvDTSRuSxi6rfnjPMMnLOtH/mub7nTTbglKD4a82IezgLjcJS05XXK0wCQaKdEsIvlhKQ==", "license": "BSD", "dependencies": {"clarinet": "~0.8.0"}}, "node_modules/qs": {"version": "0.6.6", "resolved": "https://registry.npmjs.org/qs/-/qs-0.6.6.tgz", "integrity": "sha512-kN+yNdAf29Jgp+AYHUmC7X4QdJPR8czuMWLNLc0aRxkQ7tB3vJQEONKKT9ou/rW7EbqVec11srC9q9BiVbcnHA==", "engines": {"node": "*"}}, "node_modules/request": {"version": "2.34.0", "resolved": "https://registry.npmjs.org/request/-/request-2.34.0.tgz", "integrity": "sha512-mD5mNhfkeaKMg5ZY/hZFbW4lyC/NTn34/ILGQr/XLSuxYOE6vJfL0MTPPXZcZrdt+Nh1Kce+f4B4KbGThIETxQ==", "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "engines": ["node >= 0.8.0"], "license": "Apache, Version 2.0", "dependencies": {"forever-agent": "~0.5.0", "json-stringify-safe": "~5.0.0", "mime": "~1.2.9", "node-uuid": "~1.4.0", "qs": "~0.6.0"}, "optionalDependencies": {"aws-sign2": "~0.5.0", "form-data": "~0.1.0", "hawk": "~1.0.0", "http-signature": "~0.10.0", "oauth-sign": "~0.3.0", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.3.0"}}, "node_modules/safe-buffer": {"version": "5.2.1", "resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/sntp": {"version": "0.2.4", "resolved": "https://registry.npmjs.org/sntp/-/sntp-0.2.4.tgz", "integrity": "sha512-bDLrKa/ywz65gCl+LmOiIhteP1bhEsAAzhfMedPoiHP3dyYnAevlaJshdqb9Yu0sRifyP/fRqSt8t+5qGIWlGQ==", "deprecated": "This module moved to @hapi/sntp. Please make sure to switch over as this distribution is no longer supported and may contain bugs and critical security issues.", "optional": true, "dependencies": {"hoek": "0.9.x"}, "engines": {"node": ">=0.8.0"}}, "node_modules/sockjs": {"version": "0.3.24", "resolved": "https://registry.npmjs.org/sockjs/-/sockjs-0.3.24.tgz", "integrity": "sha512-GJgLTZ7vYb/JtPSSZ10hsOYIvEYsjbNU+zPdIHcUaWVNUEPivzxku31865sSSud0Da0W4lEeOPlmw93zLQchuQ==", "license": "MIT", "dependencies": {"faye-websocket": "^0.11.3", "uuid": "^8.3.2", "websocket-driver": "^0.7.4"}}, "node_modules/sockjs/node_modules/uuid": {"version": "8.3.2", "resolved": "https://registry.npmjs.org/uuid/-/uuid-8.3.2.tgz", "integrity": "sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg==", "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/tldts": {"version": "6.1.86", "resolved": "https://registry.npmjs.org/tldts/-/tldts-6.1.86.tgz", "integrity": "sha512-WMi/OQ2axVTf/ykqCQgXiIct+mSQDFdH2fkwhPwgEwvJ1kSzZRiinb0zF2Xb8u4+OqPChmyI6MEu4EezNJz+FQ==", "license": "MIT", "optional": true, "dependencies": {"tldts-core": "^6.1.86"}, "bin": {"tldts": "bin/cli.js"}}, "node_modules/tldts-core": {"version": "6.1.86", "resolved": "https://registry.npmjs.org/tldts-core/-/tldts-core-6.1.86.tgz", "integrity": "sha512-Je6p7pkk+KMzMv2XXKmAE3McmolOQFdxkKw0R8EYNr7sELW46JqnNeTX8ybPiQgvg1ymCoF8LXs5fzFaZvJPTA==", "license": "MIT", "optional": true}, "node_modules/tough-cookie": {"version": "5.1.2", "resolved": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-5.1.2.tgz", "integrity": "sha512-FVDYdxtnj0G6Qm/DhNPSb8Ju59ULcup3tuJxkFb5K8Bv2pUXILbf0xZWU8PX8Ov19OXljbUyveOFwRMwkXzO+A==", "license": "BSD-3-<PERSON><PERSON>", "optional": true, "dependencies": {"tldts": "^6.1.32"}, "engines": {"node": ">=16"}}, "node_modules/tunnel-agent": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/tunnel-agent/-/tunnel-agent-0.3.0.tgz", "integrity": "sha512-jlGqHGoKzyyjhwv/c9omAgohntThMcGtw8RV/RDLlkbbc08kni/akVxO62N8HaXMVbVsK1NCnpSK3N2xCt22ww==", "optional": true, "engines": {"node": "*"}}, "node_modules/uuid": {"version": "1.4.2", "resolved": "https://registry.npmjs.org/uuid/-/uuid-1.4.2.tgz", "integrity": "sha512-woV5Ei+GBJyrqMXt0mJ9p8/I+47LYKp/4urH76FNTMjl22EhLPz1tNrQufTsrFf/PYV/7ctSZYAK7fKPWQKg+Q==", "deprecated": "Please upgrade to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details."}, "node_modules/websocket-driver": {"version": "0.7.4", "resolved": "https://registry.npmjs.org/websocket-driver/-/websocket-driver-0.7.4.tgz", "integrity": "sha512-b17KeDIQVjvb0ssuSDF2cYXSg2iztliJ4B9WdsuB6J952qCPKmnVq4DyW5motImXHDC1cBT/1UezrJVsKw5zjg==", "license": "Apache-2.0", "dependencies": {"http-parser-js": ">=0.5.1", "safe-buffer": ">=5.1.0", "websocket-extensions": ">=0.1.1"}, "engines": {"node": ">=0.8.0"}}, "node_modules/websocket-extensions": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/websocket-extensions/-/websocket-extensions-0.1.4.tgz", "integrity": "sha512-OqedPIGOfsDlo31UNwYbCFMSaO9m9G/0faIHj5/dZFDMFqPTcx6UwqyOy3COEaEOg/9VsGIpdqn62W5KhoKSpg==", "license": "Apache-2.0", "engines": {"node": ">=0.8.0"}}}}